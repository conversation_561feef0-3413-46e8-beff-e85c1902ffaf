
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.15.0
 * Query Engine version: 85179d7826409ee107a6ba334b5e305ae3fba9fb
 */
Prisma.prismaVersion = {
  client: "6.15.0",
  engine: "85179d7826409ee107a6ba334b5e305ae3fba9fb"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  name: 'name',
  role: 'role',
  status: 'status',
  avatarUrl: 'avatarUrl',
  hasSharedToUnlock: 'hasSharedToUnlock',
  isSubscribedToNewsletter: 'isSubscribedToNewsletter',
  activationTimestamp: 'activationTimestamp',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SupplierApplicationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  companyName: 'companyName',
  website: 'website',
  description: 'description',
  contactName: 'contactName',
  contactEmail: 'contactEmail',
  billingEmail: 'billingEmail',
  billingAddress: 'billingAddress',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PerkScalarFieldEnum = {
  id: 'id',
  supplier: 'supplier',
  title: 'title',
  description: 'description',
  category: 'category',
  logoUrl: 'logoUrl',
  usersCount: 'usersCount',
  discountAmount: 'discountAmount',
  affiliationLink: 'affiliationLink',
  isPremium: 'isPremium',
  isFeatured: 'isFeatured',
  isPartyPerk: 'isPartyPerk',
  status: 'status',
  shareCount: 'shareCount',
  isForNewUsersOnly: 'isForNewUsersOnly',
  activationDate: 'activationDate',
  startDate: 'startDate',
  endDate: 'endDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RedemptionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  perkId: 'perkId',
  timestamp: 'timestamp'
};

exports.Prisma.ShareEventScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  perkId: 'perkId',
  timestamp: 'timestamp'
};

exports.Prisma.PerkSubmissionScalarFieldEnum = {
  id: 'id',
  supplierName: 'supplierName',
  supplierId: 'supplierId',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  perkSupplier: 'perkSupplier',
  perkTitle: 'perkTitle',
  perkDescription: 'perkDescription',
  perkCategory: 'perkCategory',
  perkLogoUrl: 'perkLogoUrl',
  perkUsersCount: 'perkUsersCount',
  perkDiscountAmount: 'perkDiscountAmount',
  perkAffiliationLink: 'perkAffiliationLink',
  perkIsPremium: 'perkIsPremium',
  perkIsFeatured: 'perkIsFeatured',
  perkIsPartyPerk: 'perkIsPartyPerk',
  perkShareCount: 'perkShareCount',
  perkIsForNewUsersOnly: 'perkIsForNewUsersOnly',
  perkStartDate: 'perkStartDate',
  perkEndDate: 'perkEndDate'
};

exports.Prisma.AppNotificationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  message: 'message',
  timestamp: 'timestamp',
  read: 'read'
};

exports.Prisma.SmtpSettingsScalarFieldEnum = {
  id: 'id',
  host: 'host',
  port: 'port',
  secure: 'secure',
  username: 'username',
  password: 'password',
  fromEmail: 'fromEmail',
  redemptionLimit: 'redemptionLimit',
  isRedemptionLimitEnabled: 'isRedemptionLimitEnabled',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EmailTemplateScalarFieldEnum = {
  id: 'id',
  name: 'name',
  subject: 'subject',
  body: 'body',
  variables: 'variables',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CategoryScalarFieldEnum = {
  id: 'id',
  name: 'name',
  order: 'order',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};
exports.UserRole = exports.$Enums.UserRole = {
  User: 'User',
  Supplier: 'Supplier',
  Admin: 'Admin'
};

exports.UserStatus = exports.$Enums.UserStatus = {
  Active: 'Active',
  Banned: 'Banned',
  Pending: 'Pending'
};

exports.PerkStatus = exports.$Enums.PerkStatus = {
  Active: 'Active',
  Inactive: 'Inactive'
};

exports.SubmissionStatus = exports.$Enums.SubmissionStatus = {
  Pending: 'Pending',
  Approved: 'Approved',
  Denied: 'Denied'
};

exports.Prisma.ModelName = {
  User: 'User',
  SupplierApplication: 'SupplierApplication',
  Perk: 'Perk',
  Redemption: 'Redemption',
  ShareEvent: 'ShareEvent',
  PerkSubmission: 'PerkSubmission',
  AppNotification: 'AppNotification',
  SmtpSettings: 'SmtpSettings',
  EmailTemplate: 'EmailTemplate',
  Category: 'Category'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
