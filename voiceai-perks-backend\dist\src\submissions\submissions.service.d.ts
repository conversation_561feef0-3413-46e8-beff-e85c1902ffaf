import { PrismaService } from '../prisma/prisma.service';
import { SubmissionStatus } from '@prisma/client';
export declare class SubmissionsService {
    private prisma;
    constructor(prisma: PrismaService);
    getAllSubmissions(): Promise<({
        supplier: {
            id: string;
            name: string;
            createdAt: Date;
            updatedAt: Date;
            status: import("@prisma/client").$Enums.UserStatus;
            email: string;
            role: import("@prisma/client").$Enums.UserRole;
            avatarUrl: string | null;
            hasSharedToUnlock: boolean;
            isSubscribedToNewsletter: boolean;
            activationTimestamp: Date | null;
        };
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        status: import("@prisma/client").$Enums.SubmissionStatus;
        supplierName: string;
        supplierId: string;
        perkSupplier: string;
        perkTitle: string;
        perkDescription: string;
        perkCategory: string;
        perkLogoUrl: string;
        perkUsersCount: number | null;
        perkDiscountAmount: string;
        perkAffiliationLink: string;
        perkIsPremium: boolean;
        perkIsFeatured: boolean;
        perkIsPartyPerk: boolean;
        perkShareCount: number;
        perkIsForNewUsersOnly: boolean;
        perkStartDate: Date | null;
        perkEndDate: Date | null;
    })[]>;
    getSubmissionsBySupplier(supplierId: string): Promise<({
        supplier: {
            id: string;
            name: string;
            createdAt: Date;
            updatedAt: Date;
            status: import("@prisma/client").$Enums.UserStatus;
            email: string;
            role: import("@prisma/client").$Enums.UserRole;
            avatarUrl: string | null;
            hasSharedToUnlock: boolean;
            isSubscribedToNewsletter: boolean;
            activationTimestamp: Date | null;
        };
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        status: import("@prisma/client").$Enums.SubmissionStatus;
        supplierName: string;
        supplierId: string;
        perkSupplier: string;
        perkTitle: string;
        perkDescription: string;
        perkCategory: string;
        perkLogoUrl: string;
        perkUsersCount: number | null;
        perkDiscountAmount: string;
        perkAffiliationLink: string;
        perkIsPremium: boolean;
        perkIsFeatured: boolean;
        perkIsPartyPerk: boolean;
        perkShareCount: number;
        perkIsForNewUsersOnly: boolean;
        perkStartDate: Date | null;
        perkEndDate: Date | null;
    })[]>;
    getSubmissionById(id: number): Promise<{
        supplier: {
            id: string;
            name: string;
            createdAt: Date;
            updatedAt: Date;
            status: import("@prisma/client").$Enums.UserStatus;
            email: string;
            role: import("@prisma/client").$Enums.UserRole;
            avatarUrl: string | null;
            hasSharedToUnlock: boolean;
            isSubscribedToNewsletter: boolean;
            activationTimestamp: Date | null;
        };
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        status: import("@prisma/client").$Enums.SubmissionStatus;
        supplierName: string;
        supplierId: string;
        perkSupplier: string;
        perkTitle: string;
        perkDescription: string;
        perkCategory: string;
        perkLogoUrl: string;
        perkUsersCount: number | null;
        perkDiscountAmount: string;
        perkAffiliationLink: string;
        perkIsPremium: boolean;
        perkIsFeatured: boolean;
        perkIsPartyPerk: boolean;
        perkShareCount: number;
        perkIsForNewUsersOnly: boolean;
        perkStartDate: Date | null;
        perkEndDate: Date | null;
    }>;
    createSubmission(supplierId: string, perkData: {
        supplier: string;
        title: string;
        description: string;
        category: string;
        logoUrl: string;
        usersCount?: number;
        discountAmount: string;
        affiliationLink: string;
        isPremium?: boolean;
        isFeatured?: boolean;
        isPartyPerk?: boolean;
        isForNewUsersOnly?: boolean;
        startDate?: Date;
        endDate?: Date;
    }): Promise<{
        supplier: {
            id: string;
            name: string;
            createdAt: Date;
            updatedAt: Date;
            status: import("@prisma/client").$Enums.UserStatus;
            email: string;
            role: import("@prisma/client").$Enums.UserRole;
            avatarUrl: string | null;
            hasSharedToUnlock: boolean;
            isSubscribedToNewsletter: boolean;
            activationTimestamp: Date | null;
        };
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        status: import("@prisma/client").$Enums.SubmissionStatus;
        supplierName: string;
        supplierId: string;
        perkSupplier: string;
        perkTitle: string;
        perkDescription: string;
        perkCategory: string;
        perkLogoUrl: string;
        perkUsersCount: number | null;
        perkDiscountAmount: string;
        perkAffiliationLink: string;
        perkIsPremium: boolean;
        perkIsFeatured: boolean;
        perkIsPartyPerk: boolean;
        perkShareCount: number;
        perkIsForNewUsersOnly: boolean;
        perkStartDate: Date | null;
        perkEndDate: Date | null;
    }>;
    updateSubmission(id: number, supplierId: string, perkData: {
        supplier: string;
        title: string;
        description: string;
        category: string;
        logoUrl: string;
        usersCount?: number;
        discountAmount: string;
        affiliationLink: string;
        isPremium?: boolean;
        isFeatured?: boolean;
        isPartyPerk?: boolean;
        isForNewUsersOnly?: boolean;
        startDate?: Date;
        endDate?: Date;
    }): Promise<{
        supplier: {
            id: string;
            name: string;
            createdAt: Date;
            updatedAt: Date;
            status: import("@prisma/client").$Enums.UserStatus;
            email: string;
            role: import("@prisma/client").$Enums.UserRole;
            avatarUrl: string | null;
            hasSharedToUnlock: boolean;
            isSubscribedToNewsletter: boolean;
            activationTimestamp: Date | null;
        };
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        status: import("@prisma/client").$Enums.SubmissionStatus;
        supplierName: string;
        supplierId: string;
        perkSupplier: string;
        perkTitle: string;
        perkDescription: string;
        perkCategory: string;
        perkLogoUrl: string;
        perkUsersCount: number | null;
        perkDiscountAmount: string;
        perkAffiliationLink: string;
        perkIsPremium: boolean;
        perkIsFeatured: boolean;
        perkIsPartyPerk: boolean;
        perkShareCount: number;
        perkIsForNewUsersOnly: boolean;
        perkStartDate: Date | null;
        perkEndDate: Date | null;
    }>;
    updateSubmissionStatus(id: number, status: SubmissionStatus): Promise<{
        submission: {
            supplier: {
                id: string;
                name: string;
                createdAt: Date;
                updatedAt: Date;
                status: import("@prisma/client").$Enums.UserStatus;
                email: string;
                role: import("@prisma/client").$Enums.UserRole;
                avatarUrl: string | null;
                hasSharedToUnlock: boolean;
                isSubscribedToNewsletter: boolean;
                activationTimestamp: Date | null;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            status: import("@prisma/client").$Enums.SubmissionStatus;
            supplierName: string;
            supplierId: string;
            perkSupplier: string;
            perkTitle: string;
            perkDescription: string;
            perkCategory: string;
            perkLogoUrl: string;
            perkUsersCount: number | null;
            perkDiscountAmount: string;
            perkAffiliationLink: string;
            perkIsPremium: boolean;
            perkIsFeatured: boolean;
            perkIsPartyPerk: boolean;
            perkShareCount: number;
            perkIsForNewUsersOnly: boolean;
            perkStartDate: Date | null;
            perkEndDate: Date | null;
        };
        perk: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            category: string;
            startDate: Date | null;
            endDate: Date | null;
            usersCount: number | null;
            supplier: string;
            title: string;
            description: string;
            logoUrl: string;
            discountAmount: string;
            affiliationLink: string;
            isPremium: boolean;
            isFeatured: boolean;
            isPartyPerk: boolean;
            status: import("@prisma/client").$Enums.PerkStatus;
            shareCount: number;
            isForNewUsersOnly: boolean;
            activationDate: Date | null;
        } | null;
    }>;
    deleteSubmission(id: number, supplierId?: string): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        status: import("@prisma/client").$Enums.SubmissionStatus;
        supplierName: string;
        supplierId: string;
        perkSupplier: string;
        perkTitle: string;
        perkDescription: string;
        perkCategory: string;
        perkLogoUrl: string;
        perkUsersCount: number | null;
        perkDiscountAmount: string;
        perkAffiliationLink: string;
        perkIsPremium: boolean;
        perkIsFeatured: boolean;
        perkIsPartyPerk: boolean;
        perkShareCount: number;
        perkIsForNewUsersOnly: boolean;
        perkStartDate: Date | null;
        perkEndDate: Date | null;
    }>;
}
