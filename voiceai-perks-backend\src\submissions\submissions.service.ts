import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { SubmissionStatus, PerkStatus, UserRole, Perk } from '@prisma/client';

@Injectable()
export class SubmissionsService {
  constructor(private prisma: PrismaService) {}

  async getAllSubmissions() {
    return this.prisma.perkSubmission.findMany({
      include: {
        supplier: true,
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async getSubmissionsBySupplier(supplierId: string) {
    return this.prisma.perkSubmission.findMany({
      where: { supplierId },
      include: {
        supplier: true,
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async getSubmissionById(id: number) {
    const submission = await this.prisma.perkSubmission.findUnique({
      where: { id },
      include: {
        supplier: true,
      },
    });

    if (!submission) {
      throw new NotFoundException('Submission not found');
    }

    return submission;
  }

  async createSubmission(supplierId: string, perkData: {
    supplier: string;
    title: string;
    description: string;
    category: string;
    logoUrl: string;
    usersCount?: number;
    discountAmount: string;
    affiliationLink: string;
    isPremium?: boolean;
    isFeatured?: boolean;
    isPartyPerk?: boolean;
    isForNewUsersOnly?: boolean;
    startDate?: Date;
    endDate?: Date;
  }) {
    const supplier = await this.prisma.user.findUnique({
      where: { id: supplierId },
    });

    if (!supplier) {
      throw new NotFoundException('Supplier not found');
    }

    if (supplier.role !== UserRole.Supplier) {
      throw new ForbiddenException('Only suppliers can create submissions');
    }

    const submission = await this.prisma.perkSubmission.create({
      data: {
        supplierName: supplier.name,
        supplierId,
        status: SubmissionStatus.Pending,
        perkSupplier: perkData.supplier,
        perkTitle: perkData.title,
        perkDescription: perkData.description,
        perkCategory: perkData.category,
        perkLogoUrl: perkData.logoUrl,
        perkUsersCount: perkData.usersCount,
        perkDiscountAmount: perkData.discountAmount,
        perkAffiliationLink: perkData.affiliationLink,
        perkIsPremium: perkData.isPremium || false,
        perkIsFeatured: perkData.isFeatured || false,
        perkIsPartyPerk: perkData.isPartyPerk || false,
        perkShareCount: 0,
        perkIsForNewUsersOnly: perkData.isForNewUsersOnly || false,
        perkStartDate: perkData.startDate,
        perkEndDate: perkData.endDate,
      },
      include: {
        supplier: true,
      },
    });

    // Notify all admins
    const admins = await this.prisma.user.findMany({
      where: { role: UserRole.Admin },
    });

    const notifications = admins.map(admin => ({
      userId: admin.id,
      message: `New perk submission: "${perkData.title}".`,
      timestamp: new Date(),
      read: false,
    }));

    await this.prisma.appNotification.createMany({
      data: notifications,
    });

    return submission;
  }

  async updateSubmission(id: number, supplierId: string, perkData: {
    supplier: string;
    title: string;
    description: string;
    category: string;
    logoUrl: string;
    usersCount?: number;
    discountAmount: string;
    affiliationLink: string;
    isPremium?: boolean;
    isFeatured?: boolean;
    isPartyPerk?: boolean;
    isForNewUsersOnly?: boolean;
    startDate?: Date;
    endDate?: Date;
  }) {
    const submission = await this.prisma.perkSubmission.findUnique({
      where: { id },
    });

    if (!submission) {
      throw new NotFoundException('Submission not found');
    }

    if (submission.supplierId !== supplierId) {
      throw new ForbiddenException('You can only update your own submissions');
    }

    const updatedSubmission = await this.prisma.perkSubmission.update({
      where: { id },
      data: {
        perkSupplier: perkData.supplier,
        perkTitle: perkData.title,
        perkDescription: perkData.description,
        perkCategory: perkData.category,
        perkLogoUrl: perkData.logoUrl,
        perkUsersCount: perkData.usersCount,
        perkDiscountAmount: perkData.discountAmount,
        perkAffiliationLink: perkData.affiliationLink,
        perkIsPremium: perkData.isPremium || false,
        perkIsFeatured: perkData.isFeatured || false,
        perkIsPartyPerk: perkData.isPartyPerk || false,
        perkIsForNewUsersOnly: perkData.isForNewUsersOnly || false,
        perkStartDate: perkData.startDate,
        perkEndDate: perkData.endDate,
      },
      include: {
        supplier: true,
      },
    });

    // Notify supplier
    await this.prisma.appNotification.create({
      data: {
        userId: supplierId,
        message: `Your perk submission "${perkData.title}" has been updated.`,
        timestamp: new Date(),
        read: false,
      },
    });

    return updatedSubmission;
  }

  async updateSubmissionStatus(id: number, status: SubmissionStatus) {
    const submission = await this.prisma.perkSubmission.findUnique({
      where: { id },
      include: {
        supplier: true,
      },
    });

    if (!submission) {
      throw new NotFoundException('Submission not found');
    }

    const updatedSubmission = await this.prisma.perkSubmission.update({
      where: { id },
      data: { status },
      include: {
        supplier: true,
      },
    });

    let newPerk: Perk | null = null;

    if (status === SubmissionStatus.Approved) {
      // Create the perk
      newPerk = await this.prisma.perk.create({
        data: {
          supplier: submission.perkSupplier,
          title: submission.perkTitle,
          description: submission.perkDescription,
          category: submission.perkCategory,
          logoUrl: submission.perkLogoUrl,
          usersCount: submission.perkUsersCount,
          discountAmount: submission.perkDiscountAmount,
          affiliationLink: submission.perkAffiliationLink,
          isPremium: submission.perkIsPremium,
          isFeatured: submission.perkIsFeatured,
          isPartyPerk: submission.perkIsPartyPerk,
          status: PerkStatus.Active,
          shareCount: submission.perkShareCount,
          isForNewUsersOnly: submission.perkIsForNewUsersOnly,
          activationDate: new Date(),
          startDate: submission.perkStartDate,
          endDate: submission.perkEndDate,
        },
      });

      // Notify supplier of approval
      await this.prisma.appNotification.create({
        data: {
          userId: submission.supplierId,
          message: `Your perk submission "${submission.perkTitle}" has been approved!`,
          timestamp: new Date(),
          read: false,
        },
      });
    } else if (status === SubmissionStatus.Denied) {
      // Notify supplier of denial
      await this.prisma.appNotification.create({
        data: {
          userId: submission.supplierId,
          message: `Your perk submission "${submission.perkTitle}" has been denied.`,
          timestamp: new Date(),
          read: false,
        },
      });
    }

    return { submission: updatedSubmission, perk: newPerk };
  }

  async deleteSubmission(id: number, supplierId?: string) {
    const submission = await this.prisma.perkSubmission.findUnique({
      where: { id },
    });

    if (!submission) {
      throw new NotFoundException('Submission not found');
    }

    if (supplierId && submission.supplierId !== supplierId) {
      throw new ForbiddenException('You can only delete your own submissions');
    }

    return this.prisma.perkSubmission.delete({
      where: { id },
    });
  }
}