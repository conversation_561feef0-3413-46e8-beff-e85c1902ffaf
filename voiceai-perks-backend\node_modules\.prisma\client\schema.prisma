// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums
enum UserRole {
  User
  Supplier
  Admin
}

enum UserStatus {
  Active
  Banned
  Pending
}

enum PerkStatus {
  Active
  Inactive
}

enum SubmissionStatus {
  Pending
  Approved
  Denied
}

// Models
model User {
  id                       String     @id @db.Uuid
  email                    String     @unique
  name                     String
  role                     UserRole   @default(User)
  status                   UserStatus @default(Pending)
  avatarUrl                String?
  hasSharedToUnlock        Boolean    @default(false)
  isSubscribedToNewsletter Boolean    @default(true)
  activationTimestamp      DateTime?
  createdAt                DateTime   @default(now())
  updatedAt                DateTime   @updatedAt

  // Relations
  supplierApplication SupplierApplication?
  redeemedPerks       Redemption[]
  shareEvents         ShareEvent[]
  notifications       AppNotification[]
  perkSubmissions     PerkSubmission[]

  @@map("users")
}

model SupplierApplication {
  id             Int      @id @default(autoincrement())
  userId         String   @unique @db.Uuid
  companyName    String
  website        String
  description    String
  contactName    String
  contactEmail   String
  billingEmail   String
  billingAddress String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("supplier_applications")
}

model Perk {
  id                Int        @id @default(autoincrement())
  supplier          String
  title             String
  description       String
  category          String
  logoUrl           String
  usersCount        Int?
  discountAmount    String
  affiliationLink   String
  isPremium         Boolean    @default(false)
  isFeatured        Boolean    @default(false)
  isPartyPerk       Boolean    @default(false)
  status            PerkStatus @default(Inactive)
  shareCount        Int        @default(0)
  isForNewUsersOnly Boolean    @default(false)
  activationDate    DateTime?
  startDate         DateTime?
  endDate           DateTime?
  createdAt         DateTime   @default(now())
  updatedAt         DateTime   @updatedAt

  // Relations
  redemptions Redemption[]
  shareEvents ShareEvent[]

  @@map("perks")
}

model Redemption {
  id        Int      @id @default(autoincrement())
  userId    String   @db.Uuid
  perkId    Int
  timestamp DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  perk Perk @relation(fields: [perkId], references: [id], onDelete: Cascade)

  @@unique([userId, perkId])
  @@map("redemptions")
}

model ShareEvent {
  id        Int      @id @default(autoincrement())
  userId    String   @db.Uuid
  perkId    Int?
  timestamp DateTime @default(now())

  // Relations
  user User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  perk Perk? @relation(fields: [perkId], references: [id], onDelete: Cascade)

  @@map("share_events")
}

model PerkSubmission {
  id           Int              @id @default(autoincrement())
  supplierName String
  supplierId   String           @db.Uuid
  status       SubmissionStatus @default(Pending)
  createdAt    DateTime         @default(now())
  updatedAt    DateTime         @updatedAt

  // Perk data (embedded)
  perkSupplier          String
  perkTitle             String
  perkDescription       String
  perkCategory          String
  perkLogoUrl           String
  perkUsersCount        Int?
  perkDiscountAmount    String
  perkAffiliationLink   String
  perkIsPremium         Boolean   @default(false)
  perkIsFeatured        Boolean   @default(false)
  perkIsPartyPerk       Boolean   @default(false)
  perkShareCount        Int       @default(0)
  perkIsForNewUsersOnly Boolean   @default(false)
  perkStartDate         DateTime?
  perkEndDate           DateTime?

  // Relations
  supplier User @relation(fields: [supplierId], references: [id], onDelete: Cascade)

  @@map("perk_submissions")
}

model AppNotification {
  id        Int      @id @default(autoincrement())
  userId    String   @db.Uuid
  message   String
  timestamp DateTime @default(now())
  read      Boolean  @default(false)

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("app_notifications")
}

model SmtpSettings {
  id                       Int      @id @default(autoincrement())
  host                     String
  port                     Int
  secure                   Boolean
  username                 String
  password                 String?
  fromEmail                String
  redemptionLimit          Int
  isRedemptionLimitEnabled Boolean
  createdAt                DateTime @default(now())
  updatedAt                DateTime @updatedAt

  @@map("smtp_settings")
}

model EmailTemplate {
  id        String   @id
  name      String
  subject   String
  body      String
  variables String[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("email_templates")
}

model Category {
  id        Int      @id @default(autoincrement())
  name      String   @unique
  order     Int      @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("categories")
}
